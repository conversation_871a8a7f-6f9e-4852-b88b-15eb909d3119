"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import {
  Scan,
  Camera,
  History,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Volume2,
  VolumeX,
  Wifi,
  WifiOff,
  UserCheck,
  UserX,
  UserMinus,
  QrCode,
  Keyboard,
  RotateCcw,
  Users
} from "lucide-react"
import { useState, useEffect, useRef } from "react"
import type {
  Student,
  Attendance,
  AttendanceStatus,
  ScannedStudent,
  ScannerSettings,
  OfflineQueueItem,
  AttendanceRecord
} from "@/types"

// Mock data for the enhanced interface
const mockStudent: Student = {
  id: "1",
  studentId: "2021-001234",
  firstName: "Juan",
  lastName: "Dela Cruz",
  middleName: "Santos",
  email: "<EMAIL>",
  phone: "+63 ************",
  dateOfBirth: new Date("2003-05-15"),
  address: "123 Main St, Tanauan, Leyte",
  course: "Bachelor of Science in Information Technology",
  yearLevel: 3,
  section: "A",
  status: "active",
  qrCode: "QR2021001234",
  avatar: undefined,
  guardianName: "Pedro Dela Cruz",
  guardianPhone: "+63 ************",
  enrollmentDate: new Date("2021-08-15"),
  createdAt: new Date("2021-08-15"),
  updatedAt: new Date("2024-01-15")
}

const mockAttendanceHistory: AttendanceRecord[] = [
  { date: "2024-01-29", status: "present", timeIn: "8:15 AM", notes: "" },
  { date: "2024-01-28", status: "present", timeIn: "8:10 AM", notes: "" },
  { date: "2024-01-27", status: "late", timeIn: "8:35 AM", notes: "Traffic" },
  { date: "2024-01-26", status: "present", timeIn: "8:05 AM", notes: "" },
  { date: "2024-01-25", status: "absent", notes: "Sick leave" },
]

const mockRecentScans: ScannedStudent[] = [
  {
    id: "1",
    student: mockStudent,
    attendance: {
      id: "att1",
      studentId: "2021-001234",
      date: new Date(),
      timeIn: new Date(),
      status: "present",
      scannedBy: "teacher1",
      createdAt: new Date(),
      updatedAt: new Date()
    },
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    scanMethod: "qr"
  },
  {
    id: "2",
    student: {
      ...mockStudent,
      id: "2",
      studentId: "2021-001235",
      firstName: "Maria",
      lastName: "Santos",
      course: "Bachelor of Science in Business Administration",
      yearLevel: 2,
      section: "B"
    },
    attendance: {
      id: "att2",
      studentId: "2021-001235",
      date: new Date(),
      timeIn: new Date(),
      status: "present",
      scannedBy: "teacher1",
      createdAt: new Date(),
      updatedAt: new Date()
    },
    timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
    scanMethod: "qr"
  }
]

export default function ScanPage() {
  const { toast } = useToast()

  // Scanner state
  const [isScanning, setIsScanning] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)
  const [manualStudentId, setManualStudentId] = useState("")
  const [recentScans, setRecentScans] = useState<ScannedStudent[]>(mockRecentScans)
  const [offlineQueue, setOfflineQueue] = useState<OfflineQueueItem[]>([])

  // Scanner settings
  const [settings, setSettings] = useState<ScannerSettings>({
    soundEnabled: true,
    batchMode: false,
    autoMarkPresent: true,
    offlineMode: false,
    cameraPermission: 'prompt'
  })

  // Refs for camera and audio
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  // Check online status
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleStartScan = async () => {
    setIsScanning(true)

    try {
      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
      setSettings(prev => ({ ...prev, cameraPermission: 'granted' }))

      // Simulate scanning process
      setTimeout(() => {
        handleScanSuccess(mockStudent)
        stream.getTracks().forEach(track => track.stop())
      }, 3000)

    } catch (error) {
      setSettings(prev => ({ ...prev, cameraPermission: 'denied' }))
      setIsScanning(false)
      toast({
        title: "Camera Access Denied",
        description: "Please allow camera access to scan QR codes.",
        variant: "destructive"
      })
    }
  }

  const handleScanSuccess = (student: Student) => {
    setIsScanning(false)
    setIsProcessing(true)
    setCurrentStudent(student)

    // Play success sound
    if (settings.soundEnabled && audioRef.current) {
      audioRef.current.play().catch(() => {})
    }

    // Auto mark present if enabled
    if (settings.autoMarkPresent) {
      setTimeout(() => {
        handleMarkAttendance('present')
      }, 1000)
    } else {
      setIsProcessing(false)
    }

    toast({
      title: "Student Found",
      description: `${student.firstName} ${student.lastName} - ${student.studentId}`,
    })
  }

  const handleManualEntry = () => {
    if (!manualStudentId.trim()) {
      toast({
        title: "Invalid Input",
        description: "Please enter a valid student ID.",
        variant: "destructive"
      })
      return
    }

    // Simulate finding student by ID
    const student = { ...mockStudent, studentId: manualStudentId }
    handleScanSuccess(student)
    setManualStudentId("")
  }

  const handleMarkAttendance = (status: AttendanceStatus) => {
    if (!currentStudent) return

    const newScan: ScannedStudent = {
      id: Date.now().toString(),
      student: currentStudent,
      attendance: {
        id: Date.now().toString(),
        studentId: currentStudent.studentId,
        date: new Date(),
        timeIn: status === 'present' || status === 'late' ? new Date() : undefined,
        status,
        scannedBy: "current-teacher",
        createdAt: new Date(),
        updatedAt: new Date()
      },
      timestamp: new Date(),
      scanMethod: manualStudentId ? 'manual' : 'qr'
    }

    if (!isOnline) {
      // Add to offline queue
      const queueItem: OfflineQueueItem = {
        id: Date.now().toString(),
        studentId: currentStudent.studentId,
        action: status,
        timestamp: new Date(),
        location: "Classroom A"
      }
      setOfflineQueue(prev => [...prev, queueItem])
    }

    setRecentScans(prev => [newScan, ...prev.slice(0, 9)]) // Keep last 10
    setCurrentStudent(null)
    setIsProcessing(false)

    const statusMessages = {
      present: "marked as Present",
      absent: "marked as Absent",
      late: "marked as Late",
      excused: "marked as Excused"
    }

    toast({
      title: "Attendance Recorded",
      description: `${currentStudent.firstName} ${currentStudent.lastName} ${statusMessages[status]}`,
    })
  }

  const getAttendanceIcon = (status: AttendanceStatus) => {
    switch (status) {
      case "present":
        return <UserCheck className="h-4 w-4 text-green-600" />
      case "absent":
        return <UserX className="h-4 w-4 text-red-600" />
      case "late":
        return <Clock className="h-4 w-4 text-yellow-600" />
      case "excused":
        return <UserMinus className="h-4 w-4 text-blue-600" />
      default:
        return <User className="h-4 w-4 text-gray-600" />
    }
  }

  const getAttendanceBadgeVariant = (status: AttendanceStatus) => {
    switch (status) {
      case "present":
        return "default"
      case "absent":
        return "destructive"
      case "late":
        return "secondary"
      case "excused":
        return "outline"
      default:
        return "secondary"
    }
  }

  return (
    <MainLayout title="QR Code Scanner">
      {/* Hidden audio element for scan sounds */}
      <audio ref={audioRef} preload="auto">
        <source src="/sounds/beep.mp3" type="audio/mpeg" />
      </audio>

      <div className="space-y-6">
        {/* Scanner Controls Header */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {isOnline ? (
                <Wifi className="h-5 w-5 text-green-600" />
              ) : (
                <WifiOff className="h-5 w-5 text-red-600" />
              )}
              <span className="text-sm font-medium">
                {isOnline ? 'Online' : 'Offline'}
              </span>
              {offlineQueue.length > 0 && (
                <Badge variant="secondary">
                  {offlineQueue.length} queued
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Switch
                checked={settings.soundEnabled}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, soundEnabled: checked }))
                }
              />
              {settings.soundEnabled ? (
                <Volume2 className="h-4 w-4" />
              ) : (
                <VolumeX className="h-4 w-4" />
              )}
              <span className="text-sm">Sound</span>
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={settings.batchMode}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, batchMode: checked }))
                }
              />
              <Users className="h-4 w-4" />
              <span className="text-sm">Batch Mode</span>
            </div>
          </div>
        </div>

        {/* Main Scanner Section */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Scanner Card */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code Scanner
              </CardTitle>
              <CardDescription>
                Scan student QR codes or enter student ID manually
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Camera Preview Area */}
              <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 overflow-hidden">
                {isScanning ? (
                  <div className="relative w-full h-full">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-48 h-48 border-2 border-blue-500 rounded-lg animate-pulse">
                        <div className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-blue-500"></div>
                        <div className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-blue-500"></div>
                        <div className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-blue-500"></div>
                        <div className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-blue-500"></div>
                      </div>
                    </div>
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
                      Scanning for QR codes...
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <Camera className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium text-muted-foreground mb-2">
                      Camera Preview
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Click &quot;Start Scanning&quot; to begin
                    </p>
                  </div>
                )}
              </div>

              {/* Scanner Controls */}
              <div className="space-y-4">
                <Button
                  onClick={handleStartScan}
                  disabled={isScanning || isProcessing}
                  className="w-full h-12 text-lg"
                  size="lg"
                >
                  <Camera className="h-5 w-5 mr-2" />
                  {isScanning ? "Scanning..." : "Start Scanning"}
                </Button>

                <div className="relative">
                  <Separator className="my-4" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="bg-background px-2 text-sm text-muted-foreground">
                      OR
                    </span>
                  </div>
                </div>

                {/* Manual Entry */}
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter Student ID manually"
                    value={manualStudentId}
                    onChange={(e) => setManualStudentId(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleManualEntry()}
                    disabled={isProcessing}
                  />
                  <Button
                    onClick={handleManualEntry}
                    disabled={!manualStudentId.trim() || isProcessing}
                    variant="outline"
                  >
                    <Keyboard className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Student Profile & Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Student Profile
              </CardTitle>
              <CardDescription>
                Scanned student information and actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {currentStudent ? (
                <div className="space-y-6">
                  {/* Student Info */}
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={currentStudent.avatar} />
                      <AvatarFallback className="text-lg">
                        {currentStudent.firstName[0]}{currentStudent.lastName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <h3 className="text-lg font-semibold">
                        {currentStudent.firstName} {currentStudent.lastName}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {currentStudent.course}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span>ID: {currentStudent.studentId}</span>
                        <span>Year {currentStudent.yearLevel} - Section {currentStudent.section}</span>
                      </div>
                      <Badge variant={currentStudent.status === 'active' ? 'default' : 'secondary'}>
                        {currentStudent.status}
                      </Badge>
                    </div>
                  </div>

                  {/* Attendance History */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Recent Attendance (Last 5 days)</h4>
                    <div className="space-y-2">
                      {mockAttendanceHistory.map((record, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="flex items-center gap-2">
                            {getAttendanceIcon(record.status)}
                            <span className="text-sm">{record.date}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getAttendanceBadgeVariant(record.status)}>
                              {record.status}
                            </Badge>
                            {record.timeIn && (
                              <span className="text-xs text-muted-foreground">
                                {record.timeIn}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Mark Attendance</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        onClick={() => handleMarkAttendance('present')}
                        disabled={isProcessing}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <UserCheck className="h-4 w-4 mr-2" />
                        Present
                      </Button>
                      <Button
                        onClick={() => handleMarkAttendance('late')}
                        disabled={isProcessing}
                        variant="outline"
                        className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        Late
                      </Button>
                      <Button
                        onClick={() => handleMarkAttendance('absent')}
                        disabled={isProcessing}
                        variant="outline"
                        className="border-red-600 text-red-600 hover:bg-red-50"
                      >
                        <UserX className="h-4 w-4 mr-2" />
                        Absent
                      </Button>
                      <Button
                        onClick={() => handleMarkAttendance('excused')}
                        disabled={isProcessing}
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-50"
                      >
                        <UserMinus className="h-4 w-4 mr-2" />
                        Excused
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <User className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium text-muted-foreground mb-2">
                    No Student Selected
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Scan a QR code or enter a student ID to view profile
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Today's Scanned Students & Statistics */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Today's Scanned Students */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Today&apos;s Scanned Students
              </CardTitle>
              <CardDescription>
                Students scanned today with timestamps
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {recentScans.length > 0 ? (
                  recentScans.map((scan) => (
                    <div
                      key={scan.id}
                      className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={scan.student.avatar} />
                          <AvatarFallback>
                            {scan.student.firstName[0]}{scan.student.lastName[0]}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {scan.student.firstName} {scan.student.lastName}
                            </span>
                            <Badge variant={getAttendanceBadgeVariant(scan.attendance.status)}>
                              {scan.attendance.status}
                            </Badge>
                            {scan.scanMethod === 'manual' && (
                              <Badge variant="outline" className="text-xs">
                                <Keyboard className="h-3 w-3 mr-1" />
                                Manual
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {scan.student.studentId} • {scan.student.course} {scan.student.yearLevel}{scan.student.section}
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="text-sm font-medium">
                            {scan.timestamp.toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {scan.attendance.timeIn?.toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            }) || 'No time in'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <History className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium text-muted-foreground mb-2">
                      No Scans Today
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Start scanning to see student records here
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Scan Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Today&apos;s Statistics</CardTitle>
              <CardDescription>
                Scanning activity overview
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {recentScans.filter(s => s.attendance.status === 'present').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Present</div>
                </div>
                <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {recentScans.filter(s => s.attendance.status === 'late').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Late</div>
                </div>
                <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {recentScans.filter(s => s.attendance.status === 'absent').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Absent</div>
                </div>
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {recentScans.filter(s => s.attendance.status === 'excused').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Excused</div>
                </div>
              </div>

              {/* Success Rate */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Success Rate</span>
                  <span className="font-medium">
                    {recentScans.length > 0 ? '100%' : '0%'}
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: recentScans.length > 0 ? "100%" : "0%" }}
                  ></div>
                </div>
              </div>

              {/* Session Info */}
              <div className="pt-4 border-t space-y-2">
                <div className="text-sm text-muted-foreground">Scan Session</div>
                <div className="text-lg font-semibold">
                  Started at {new Date().toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
                <div className="text-sm text-muted-foreground">
                  Total Scans: {recentScans.length}
                </div>

                {/* Offline Queue Status */}
                {offlineQueue.length > 0 && (
                  <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
                      <WifiOff className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {offlineQueue.length} items in offline queue
                      </span>
                    </div>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                      Will sync when connection is restored
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
