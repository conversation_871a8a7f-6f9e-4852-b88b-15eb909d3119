[{"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\analytics\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\error.tsx": "2", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\loading.tsx": "4", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\login\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\login\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\not-found.tsx": "7", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\reports\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scan\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\settings\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\students\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\error-boundary.tsx": "13", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\header.tsx": "14", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\main-layout.tsx": "15", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\sidebar.tsx": "16", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\school-branding.tsx": "17", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\theme-provider.tsx": "18", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\theme-toggle.tsx": "19", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert.tsx": "20", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\avatar.tsx": "21", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\badge.tsx": "22", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\button.tsx": "23", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\card.tsx": "24", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\checkbox.tsx": "25", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\form.tsx": "26", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\input.tsx": "27", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\label.tsx": "28", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\loading.tsx": "29", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\progress.tsx": "30", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\separator.tsx": "31", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sheet.tsx": "32", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\switch.tsx": "33", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\toast.tsx": "34", "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\toaster.tsx": "35", "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils.ts": "36"}, {"size": 11459, "mtime": 1753980287328, "results": "37", "hashOfConfig": "38"}, {"size": 2212, "mtime": 1753980442733, "results": "39", "hashOfConfig": "38"}, {"size": 968, "mtime": 1753982836403, "results": "40", "hashOfConfig": "38"}, {"size": 147, "mtime": 1753980430908, "results": "41", "hashOfConfig": "38"}, {"size": 361, "mtime": 1753981354202, "results": "42", "hashOfConfig": "38"}, {"size": 12349, "mtime": 1753981423425, "results": "43", "hashOfConfig": "38"}, {"size": 1846, "mtime": 1753980452654, "results": "44", "hashOfConfig": "38"}, {"size": 24112, "mtime": 1753982441919, "results": "45", "hashOfConfig": "38"}, {"size": 10815, "mtime": 1753980327813, "results": "46", "hashOfConfig": "38"}, {"size": 28088, "mtime": 1753983107010, "results": "47", "hashOfConfig": "38"}, {"size": 14629, "mtime": 1753980374080, "results": "48", "hashOfConfig": "38"}, {"size": 9583, "mtime": 1753980218591, "results": "49", "hashOfConfig": "38"}, {"size": 5684, "mtime": 1753980425426, "results": "50", "hashOfConfig": "38"}, {"size": 2417, "mtime": 1753982468387, "results": "51", "hashOfConfig": "38"}, {"size": 972, "mtime": 1753980115540, "results": "52", "hashOfConfig": "38"}, {"size": 2811, "mtime": 1753980528611, "results": "53", "hashOfConfig": "38"}, {"size": 2899, "mtime": 1753980476001, "results": "54", "hashOfConfig": "38"}, {"size": 327, "mtime": 1753980073884, "results": "55", "hashOfConfig": "38"}, {"size": 688, "mtime": 1753980079924, "results": "56", "hashOfConfig": "38"}, {"size": 1584, "mtime": 1753981850499, "results": "57", "hashOfConfig": "38"}, {"size": 1419, "mtime": 1753982596101, "results": "58", "hashOfConfig": "38"}, {"size": 1128, "mtime": 1753981850449, "results": "59", "hashOfConfig": "38"}, {"size": 1901, "mtime": 1753981210205, "results": "60", "hashOfConfig": "38"}, {"size": 1877, "mtime": 1753979983138, "results": "61", "hashOfConfig": "38"}, {"size": 1070, "mtime": 1753981191817, "results": "62", "hashOfConfig": "38"}, {"size": 4120, "mtime": 1753981191863, "results": "63", "hashOfConfig": "38"}, {"size": 791, "mtime": 1753981191779, "results": "64", "hashOfConfig": "38"}, {"size": 724, "mtime": 1753981191804, "results": "65", "hashOfConfig": "38"}, {"size": 3539, "mtime": 1753980399587, "results": "66", "hashOfConfig": "38"}, {"size": 791, "mtime": 1753981850481, "results": "67", "hashOfConfig": "38"}, {"size": 770, "mtime": 1753981850509, "results": "68", "hashOfConfig": "38"}, {"size": 4312, "mtime": 1753980739821, "results": "69", "hashOfConfig": "38"}, {"size": 1139, "mtime": 1753980031790, "results": "70", "hashOfConfig": "38"}, {"size": 4859, "mtime": 1753982596158, "results": "71", "hashOfConfig": "38"}, {"size": 786, "mtime": 1753982987701, "results": "72", "hashOfConfig": "38"}, {"size": 959, "mtime": 1753979962265, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dlz1ck", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\login\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\not-found.tsx", ["182", "183", "184"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\page.tsx", ["185", "186"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\scan\\page.tsx", ["187", "188"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\app\\students\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\error-boundary.tsx", ["189", "190", "191"], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\main-layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\school-branding.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\qrsams\\lib\\utils.ts", [], [], {"ruleId": "192", "severity": 2, "message": "193", "line": 18, "column": 25, "nodeType": "194", "messageId": "195", "suggestions": "196"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 18, "column": 46, "nodeType": "194", "messageId": "195", "suggestions": "197"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 28, "column": 26, "nodeType": "194", "messageId": "195", "suggestions": "198"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 422, "column": 31, "nodeType": "194", "messageId": "195", "suggestions": "199"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 585, "column": 22, "nodeType": "194", "messageId": "195", "suggestions": "200"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 583, "column": 22, "nodeType": "194", "messageId": "195", "suggestions": "201"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 660, "column": 31, "nodeType": "194", "messageId": "195", "suggestions": "202"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 132, "column": 21, "nodeType": "194", "messageId": "195", "suggestions": "203"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 132, "column": 42, "nodeType": "194", "messageId": "195", "suggestions": "204"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 152, "column": 16, "nodeType": "194", "messageId": "195", "suggestions": "205"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["206", "207", "208", "209"], ["210", "211", "212", "213"], ["214", "215", "216", "217"], ["218", "219", "220", "221"], ["222", "223", "224", "225"], ["226", "227", "228", "229"], ["230", "231", "232", "233"], ["234", "235", "236", "237"], ["238", "239", "240", "241"], ["242", "243", "244", "245"], {"messageId": "246", "data": "247", "fix": "248", "desc": "249"}, {"messageId": "246", "data": "250", "fix": "251", "desc": "252"}, {"messageId": "246", "data": "253", "fix": "254", "desc": "255"}, {"messageId": "246", "data": "256", "fix": "257", "desc": "258"}, {"messageId": "246", "data": "259", "fix": "260", "desc": "249"}, {"messageId": "246", "data": "261", "fix": "262", "desc": "252"}, {"messageId": "246", "data": "263", "fix": "264", "desc": "255"}, {"messageId": "246", "data": "265", "fix": "266", "desc": "258"}, {"messageId": "246", "data": "267", "fix": "268", "desc": "249"}, {"messageId": "246", "data": "269", "fix": "270", "desc": "252"}, {"messageId": "246", "data": "271", "fix": "272", "desc": "255"}, {"messageId": "246", "data": "273", "fix": "274", "desc": "258"}, {"messageId": "246", "data": "275", "fix": "276", "desc": "249"}, {"messageId": "246", "data": "277", "fix": "278", "desc": "252"}, {"messageId": "246", "data": "279", "fix": "280", "desc": "255"}, {"messageId": "246", "data": "281", "fix": "282", "desc": "258"}, {"messageId": "246", "data": "283", "fix": "284", "desc": "249"}, {"messageId": "246", "data": "285", "fix": "286", "desc": "252"}, {"messageId": "246", "data": "287", "fix": "288", "desc": "255"}, {"messageId": "246", "data": "289", "fix": "290", "desc": "258"}, {"messageId": "246", "data": "291", "fix": "292", "desc": "249"}, {"messageId": "246", "data": "293", "fix": "294", "desc": "252"}, {"messageId": "246", "data": "295", "fix": "296", "desc": "255"}, {"messageId": "246", "data": "297", "fix": "298", "desc": "258"}, {"messageId": "246", "data": "299", "fix": "300", "desc": "249"}, {"messageId": "246", "data": "301", "fix": "302", "desc": "252"}, {"messageId": "246", "data": "303", "fix": "304", "desc": "255"}, {"messageId": "246", "data": "305", "fix": "306", "desc": "258"}, {"messageId": "246", "data": "307", "fix": "308", "desc": "249"}, {"messageId": "246", "data": "309", "fix": "310", "desc": "252"}, {"messageId": "246", "data": "311", "fix": "312", "desc": "255"}, {"messageId": "246", "data": "313", "fix": "314", "desc": "258"}, {"messageId": "246", "data": "315", "fix": "316", "desc": "249"}, {"messageId": "246", "data": "317", "fix": "318", "desc": "252"}, {"messageId": "246", "data": "319", "fix": "320", "desc": "255"}, {"messageId": "246", "data": "321", "fix": "322", "desc": "258"}, {"messageId": "246", "data": "323", "fix": "324", "desc": "249"}, {"messageId": "246", "data": "325", "fix": "326", "desc": "252"}, {"messageId": "246", "data": "327", "fix": "328", "desc": "255"}, {"messageId": "246", "data": "329", "fix": "330", "desc": "258"}, "replaceWithAlt", {"alt": "331"}, {"range": "332", "text": "333"}, "Replace with `&apos;`.", {"alt": "334"}, {"range": "335", "text": "336"}, "Replace with `&lsquo;`.", {"alt": "337"}, {"range": "338", "text": "339"}, "Replace with `&#39;`.", {"alt": "340"}, {"range": "341", "text": "342"}, "Replace with `&rsquo;`.", {"alt": "331"}, {"range": "343", "text": "344"}, {"alt": "334"}, {"range": "345", "text": "346"}, {"alt": "337"}, {"range": "347", "text": "348"}, {"alt": "340"}, {"range": "349", "text": "350"}, {"alt": "331"}, {"range": "351", "text": "352"}, {"alt": "334"}, {"range": "353", "text": "354"}, {"alt": "337"}, {"range": "355", "text": "356"}, {"alt": "340"}, {"range": "357", "text": "358"}, {"alt": "331"}, {"range": "359", "text": "360"}, {"alt": "334"}, {"range": "361", "text": "362"}, {"alt": "337"}, {"range": "363", "text": "364"}, {"alt": "340"}, {"range": "365", "text": "366"}, {"alt": "331"}, {"range": "367", "text": "368"}, {"alt": "334"}, {"range": "369", "text": "370"}, {"alt": "337"}, {"range": "371", "text": "372"}, {"alt": "340"}, {"range": "373", "text": "374"}, {"alt": "331"}, {"range": "375", "text": "376"}, {"alt": "334"}, {"range": "377", "text": "378"}, {"alt": "337"}, {"range": "379", "text": "380"}, {"alt": "340"}, {"range": "381", "text": "382"}, {"alt": "331"}, {"range": "383", "text": "384"}, {"alt": "334"}, {"range": "385", "text": "386"}, {"alt": "337"}, {"range": "387", "text": "388"}, {"alt": "340"}, {"range": "389", "text": "390"}, {"alt": "331"}, {"range": "391", "text": "392"}, {"alt": "334"}, {"range": "393", "text": "394"}, {"alt": "337"}, {"range": "395", "text": "396"}, {"alt": "340"}, {"range": "397", "text": "398"}, {"alt": "331"}, {"range": "399", "text": "400"}, {"alt": "334"}, {"range": "401", "text": "402"}, {"alt": "337"}, {"range": "403", "text": "404"}, {"alt": "340"}, {"range": "405", "text": "406"}, {"alt": "331"}, {"range": "407", "text": "408"}, {"alt": "334"}, {"range": "409", "text": "410"}, {"alt": "337"}, {"range": "411", "text": "412"}, {"alt": "340"}, {"range": "413", "text": "414"}, "&apos;", [807, 903], "\n            The page you&apos;re looking for doesn't exist in the TSAT Attendance System.\n          ", "&lsquo;", [807, 903], "\n            The page you&lsquo;re looking for doesn't exist in the TSAT Attendance System.\n          ", "&#39;", [807, 903], "\n            The page you&#39;re looking for doesn't exist in the TSAT Attendance System.\n          ", "&rsquo;", [807, 903], "\n            The page you&rsquo;re looking for doesn't exist in the TSAT Attendance System.\n          ", [807, 903], "\n            The page you're looking for doesn&apos;t exist in the TSAT Attendance System.\n          ", [807, 903], "\n            The page you're looking for doesn&lsquo;t exist in the TSAT Attendance System.\n          ", [807, 903], "\n            The page you're looking for doesn&#39;t exist in the TSAT Attendance System.\n          ", [807, 903], "\n            The page you're looking for doesn&rsquo;t exist in the TSAT Attendance System.\n          ", [1320, 1367], "• You don&apos;t have permission to access this page", [1320, 1367], "• You don&lsquo;t have permission to access this page", [1320, 1367], "• You don&#39;t have permission to access this page", [1320, 1367], "• You don&rsquo;t have permission to access this page", [12710, 12730], "View Today&apos;s Reports", [12710, 12730], "View Today&lsquo;s Reports", [12710, 12730], "View Today&#39;s Reports", [12710, 12730], "View Today&rsquo;s Reports", [19752, 19833], "\n                Today&apos;s scheduled classes and expected attendance\n              ", [19752, 19833], "\n                Today&lsquo;s scheduled classes and expected attendance\n              ", [19752, 19833], "\n                Today&#39;s scheduled classes and expected attendance\n              ", [19752, 19833], "\n                Today&rsquo;s scheduled classes and expected attendance\n              ", [20592, 20648], "\n                Today&apos;s Scanned Students\n              ", [20592, 20648], "\n                Today&lsquo;s Scanned Students\n              ", [20592, 20648], "\n                Today&#39;s <PERSON>anne<PERSON> Students\n              ", [20592, 20648], "\n                Today&rsquo;s Scanned Students\n              ", [24110, 24128], "Today&apos;s Statistics", [24110, 24128], "Today&lsquo;s Statistics", [24110, 24128], "Today&#39;s Statistics", [24110, 24128], "Today&rsquo;s Statistics", [4369, 4445], "\n        The page you&apos;re looking for doesn't exist or has been moved.\n      ", [4369, 4445], "\n        The page you&lsquo;re looking for doesn't exist or has been moved.\n      ", [4369, 4445], "\n        The page you&#39;re looking for doesn't exist or has been moved.\n      ", [4369, 4445], "\n        The page you&rsquo;re looking for doesn't exist or has been moved.\n      ", [4369, 4445], "\n        The page you're looking for doesn&apos;t exist or has been moved.\n      ", [4369, 4445], "\n        The page you're looking for doesn&lsquo;t exist or has been moved.\n      ", [4369, 4445], "\n        The page you're looking for doesn&#39;t exist or has been moved.\n      ", [4369, 4445], "\n        The page you're looking for doesn&rsquo;t exist or has been moved.\n      ", [5059, 5125], "\n        You don&apos;t have permission to access this resource.\n      ", [5059, 5125], "\n        You don&lsquo;t have permission to access this resource.\n      ", [5059, 5125], "\n        You don&#39;t have permission to access this resource.\n      ", [5059, 5125], "\n        You don&rsquo;t have permission to access this resource.\n      "]